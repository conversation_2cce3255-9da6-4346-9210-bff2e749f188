# DIU Routine Excel Generator

This tool generates an Excel file from the DIU routine JSON data.

## Format

The Excel file is structured as follows:
- First row: Day name
- Second row: Time slots (colspan of 3 each)
- Third row: Column titles (Building, Room, Type, Course, Section, Teacher)
- Following rows: Actual data

## Room Information Processing
- Building: Part before the hyphen (e.g., "KT" from "KT-208")
- Room: Part after the hyphen (e.g., "208" from "KT-208")
- Type: "Lab" if room name contains "lab" (case insensitive), otherwise "Theory"

## How to Run

1. Install dependencies:
```
npm install
```

2. Run the generator:
```
npm run generate
```

This will create a file named `routine_summer_2025.xlsx` containing the formatted routine data. 