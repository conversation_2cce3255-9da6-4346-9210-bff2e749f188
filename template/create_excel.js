const Excel = require('exceljs');
const fs = require('fs');

// Read the JSON data
const routineData = JSON.parse(fs.readFileSync('./organized_routine_summer_2025_new.json', 'utf8'));

// Create a new Excel workbook and worksheet
const workbook = new Excel.Workbook();
const worksheet = workbook.addWorksheet('Routine');

// Define the time slots based on the data
const timeSlots = [
  '08:30-10:00', 
  '10:00-11:30', 
  '11:30-01:00', 
  '01:00-02:30', 
  '02:30-04:00', 
  '04:00-05:30'
];

// Define days of the week from the data
const days = Object.keys(routineData);

// Function to identify building from room name
function getBuilding(roomName) {
  if (!roomName) return '';
  const parts = roomName.split('-');
  if (parts.length >= 1) {
    return parts[0].trim();
  }
  return '';
}

// Function to extract room number from room name
function getRoomNumber(roomName) {
  if (!roomName) return '';
  const parts = roomName.split('-');
  if (parts.length >= 2) {
    return parts[1].trim().split('\n')[0].split(' ')[0];
  } else {
    return parts[0].trim();
  }
}

// Function to determine if it's a lab or theory class
function getType(roomName) {
  if (!roomName) return 'Theory';
  return roomName.toLowerCase().includes('lab') ? 'Lab' : 'Theory';
}

// Define border style
const borderStyle = {
  top: { style: 'thin' },
  left: { style: 'thin' },
  bottom: { style: 'thin' },
  right: { style: 'thin' }
};

// Function to apply style to a cell
function styleCellWithBorder(cell) {
  cell.border = borderStyle;
  cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
}

// Process each day
let currentRow = 1;
let totalColumns = 3 + (timeSlots.length * 3); // Building, Room, Type + (Course, Section, Teacher) for each time slot

// Collect all unique rooms across all days and time slots
const allUniqueRooms = new Set();
days.forEach(day => {
  timeSlots.forEach(timeSlot => {
    if (routineData[day][timeSlot]) {
      Object.keys(routineData[day][timeSlot]).forEach(room => {
        allUniqueRooms.add(room);
      });
    }
  });
});

// Convert to array and sort for consistent ordering
const allRoomsArray = Array.from(allUniqueRooms).sort();

days.forEach(day => {
  // 1. First row: Day name
  worksheet.getCell(currentRow, 1).value = day;
  worksheet.mergeCells(currentRow, 1, currentRow, totalColumns);
  const dayCell = worksheet.getCell(currentRow, 1);
  dayCell.alignment = { horizontal: 'center', vertical: 'middle' };
  dayCell.font = { bold: true, size: 14 };
  dayCell.fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFD3D3D3' } // Light gray background
  };
  styleCellWithBorder(dayCell);
  
  currentRow++;
  
  // 2. Second row: Time slots with colspan of 3
  worksheet.getCell(currentRow, 1).value = '';
  worksheet.getCell(currentRow, 2).value = '';
  worksheet.getCell(currentRow, 3).value = '';
  styleCellWithBorder(worksheet.getCell(currentRow, 1));
  styleCellWithBorder(worksheet.getCell(currentRow, 2));
  styleCellWithBorder(worksheet.getCell(currentRow, 3));
  
  let currentColumn = 4;
  timeSlots.forEach(timeSlot => {
    worksheet.getCell(currentRow, currentColumn).value = timeSlot;
    worksheet.mergeCells(currentRow, currentColumn, currentRow, currentColumn + 2);
    const timeCell = worksheet.getCell(currentRow, currentColumn);
    timeCell.alignment = { horizontal: 'center', vertical: 'middle' };
    timeCell.font = { bold: true };
    timeCell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFEAEAEA' } // Lighter gray for time slots
    };
    styleCellWithBorder(timeCell);
    currentColumn += 3;
  });
  
  currentRow++;
  
  // 3. Third row: Column headers (Building, Room, Type, Course, Section, Teacher)
  worksheet.getCell(currentRow, 1).value = 'Building';
  worksheet.getCell(currentRow, 2).value = 'Room';
  worksheet.getCell(currentRow, 3).value = 'Type';
  styleCellWithBorder(worksheet.getCell(currentRow, 1));
  styleCellWithBorder(worksheet.getCell(currentRow, 2));
  styleCellWithBorder(worksheet.getCell(currentRow, 3));
  
  currentColumn = 4;
  timeSlots.forEach(() => {
    worksheet.getCell(currentRow, currentColumn).value = 'Course';
    worksheet.getCell(currentRow, currentColumn + 1).value = 'Section';
    worksheet.getCell(currentRow, currentColumn + 2).value = 'Teacher';
    
    styleCellWithBorder(worksheet.getCell(currentRow, currentColumn));
    styleCellWithBorder(worksheet.getCell(currentRow, currentColumn + 1));
    styleCellWithBorder(worksheet.getCell(currentRow, currentColumn + 2));
    
    currentColumn += 3;
  });
  
  // Style the header row
  for (let i = 1; i <= totalColumns; i++) {
    worksheet.getCell(currentRow, i).font = { bold: true };
    worksheet.getCell(currentRow, i).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFF0F0F0' } // Very light gray for header
    };
  }
  
  currentRow++;
  
  // 4. Fill in the data rows
  // Use the complete list of rooms for each day
  allRoomsArray.forEach(roomName => {
    // Get building, room number, and type once for this room
    const building = getBuilding(roomName);
    const roomNumber = getRoomNumber(roomName);
    const type = getType(roomName);
    
    // Add building, room number, and type to the first three columns
    worksheet.getCell(currentRow, 1).value = building;
    worksheet.getCell(currentRow, 2).value = roomNumber;
    worksheet.getCell(currentRow, 3).value = type;
    styleCellWithBorder(worksheet.getCell(currentRow, 1));
    styleCellWithBorder(worksheet.getCell(currentRow, 2));
    styleCellWithBorder(worksheet.getCell(currentRow, 3));
    
    // Add class data for each time slot
    let dataColumnIndex = 4;
    timeSlots.forEach(timeSlot => {
      // Look for class in this room at this time slot
      const timeSlotData = routineData[day][timeSlot];
      if (timeSlotData && timeSlotData[roomName]) {
        const classData = timeSlotData[roomName];
        // Add course, section, and teacher data
        worksheet.getCell(currentRow, dataColumnIndex).value = classData.course || '';
        worksheet.getCell(currentRow, dataColumnIndex + 1).value = classData.section || '';
        worksheet.getCell(currentRow, dataColumnIndex + 2).value = classData.teacher || '';
      } else {
        // No class at this time, add empty cells
        worksheet.getCell(currentRow, dataColumnIndex).value = '';
        worksheet.getCell(currentRow, dataColumnIndex + 1).value = '';
        worksheet.getCell(currentRow, dataColumnIndex + 2).value = '';
      }
      
      // Style the cells
      styleCellWithBorder(worksheet.getCell(currentRow, dataColumnIndex));
      styleCellWithBorder(worksheet.getCell(currentRow, dataColumnIndex + 1));
      styleCellWithBorder(worksheet.getCell(currentRow, dataColumnIndex + 2));
      
      dataColumnIndex += 3;
    });
    
    currentRow++;
  });
  
  // Add a blank row between days for better readability
  currentRow++;
});

// Set column widths for better readability
worksheet.columns.forEach((column, index) => {
  if (index === 0) { // Building column
    column.width = 15;
  } else if (index === 1) { // Room column
    column.width = 10;
  } else if (index === 2) { // Type column
    column.width = 10;
  } else if ((index - 3) % 3 === 0) { // Course columns
    column.width = 15;
  } else if ((index - 4) % 3 === 0) { // Section columns
    column.width = 12;
  } else if ((index - 5) % 3 === 0) { // Teacher columns
    column.width = 12;
  }
});

// Save the workbook
workbook.xlsx.writeFile('routine_summer_2025_v3.xlsx')
  .then(() => {
    console.log('Excel file created successfully!');
  })
  .catch(err => {
    console.error('Error creating Excel file:', err);
  }); 