const fs = require('fs');

// Function to read the original JSON file
function readRoutineFile(filePath) {
    try {
        const data = fs.readFileSync(filePath, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error reading the file:', error);
        process.exit(1);
    }
}

// Function to identify day names in an object
function isDayObject(obj) {
    const dayNames = ['SATURDAY', 'SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY'];
    
    // Check if the first key value contains any day name
    const firstKeyValue = obj["   "] || '';
    
    // Case insensitive check for any day name
    return dayNames.some(day => firstKeyValue.toUpperCase().includes(day));
}

// Function to get the cleaned day name from an object
function getDayName(obj) {
    const dayNames = ['SATURDAY', 'SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY'];
    const firstKeyValue = obj["   "] || '';
    
    for (const day of dayNames) {
        if (firstKeyValue.toUpperCase().includes(day)) {
            return day;
        }
    }
    return null;
}

// Function to process the data and generate organized JSON
function generateOrganizedRoutine(data) {
    const organizedData = {};
    let currentDay = null;
    let timeSlots = [];
    let isTimeSlotRow = false;
    let isTitleRow = false;

    // Iterate through all items in the array
    const items = data[" Class Routine Summer 2025 V_3"];
    
    for (let i = 0; i < items.length; i++) {
        const item = items[i];
        
        // Check if this is a day object
        if (isDayObject(item)) {
            currentDay = getDayName(item);
            organizedData[currentDay] = {};
            isTimeSlotRow = true;
            continue;
        }
        
        // Check if this is a time slot row
        if (isTimeSlotRow && currentDay) {
            // Get time slots - they are in the 1st, 4th, 7th, 10th, 13th, and 16th keys
            timeSlots = [
                item["   "],                // 1st key
                item["Unnamed: 3"],         // 4th key
                item["Unnamed: 6"],         // 7th key
                item["Unnamed: 9"],         // 10th key
                item["Unnamed: 12"],        // 13th key
                item["Unnamed: 15"]         // 16th key
            ].filter(slot => slot && slot.includes('-')); // Only keep non-empty time slots
            
            for (const timeSlot of timeSlots) {
                organizedData[currentDay][timeSlot] = {};
            }
            
            isTimeSlotRow = false;
            isTitleRow = true;
            continue;
        }
        
        // Skip title row
        if (isTitleRow) {
            isTitleRow = false;
            continue;
        }
        
        // Process class data rows
        if (currentDay && timeSlots.length > 0 && item["   "]) {
            const roomName = item["   "];
            
            // Process data for each time slot
            for (let slotIndex = 0; slotIndex < timeSlots.length; slotIndex++) {
                const timeSlot = timeSlots[slotIndex];
                
                // Calculate indices for course, section and teacher based on slot index
                const courseIndex = slotIndex * 3 + 1;      // 1, 4, 7, 10, 13, 16
                const teacherIndex = slotIndex * 3 + 2;     // 2, 5, 8, 11, 14, 17
                
                const course = item[`Unnamed: ${courseIndex}`];
                const teacher = item[`Unnamed: ${teacherIndex}`];
                
                // Only add if there's course data
                if (course) {
                    // Extract course code and section from format like CSE423(62_G)
                    let courseCode = course;
                    let section = '';
                    
                    const match = course.match(/([A-Za-z0-9]+)\(([^)]+)\)/);
                    if (match) {
                        courseCode = match[1];
                        section = match[2];
                    }
                    
                    // Add to organized data
                    if (!organizedData[currentDay][timeSlot][roomName]) {
                        organizedData[currentDay][timeSlot][roomName] = {};
                    }
                    
                    organizedData[currentDay][timeSlot][roomName] = {
                        course: courseCode,
                        section: section,
                        teacher: teacher
                    };
                }
            }
        }
        
        // If we encounter another day, reset the current state
        if ((i < items.length - 1) && isDayObject(items[i + 1])) {
            isTimeSlotRow = false;
            isTitleRow = false;
        }
    }
    
    return organizedData;
}

// Function to write the organized data to a new JSON file
function writeOrganizedRoutine(data, outputPath) {
    try {
        fs.writeFileSync(outputPath, JSON.stringify(data, null, 2), 'utf8');
        console.log(`Successfully wrote organized routine to ${outputPath}`);
    } catch (error) {
        console.error('Error writing the file:', error);
    }
}

// Main function
function main() {
    const inputPath = 'class_routine_summer_2025_new.json';
    const outputPath = 'organized_routine_summer_2025_new.json';
    
    // Read the original data
    const rawData = readRoutineFile(inputPath);
    
    // Generate organized data
    const organizedData = generateOrganizedRoutine(rawData);
    
    // Write organized data to a new file
    writeOrganizedRoutine(organizedData, outputPath);
}

// Execute the main function
main(); 