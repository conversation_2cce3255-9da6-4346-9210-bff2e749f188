const fs = require('fs');
const ExcelJS = require('exceljs');

// Function to load JSON data
function loadExtractedData(filePath) {
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading or parsing ${filePath}:`, error.message);
    process.exit(1);
  }
}

// Function to create and format Excel workbook with multiple sheets
async function createExcelFile(extractedData, outputPath) {
  const { metadata, assignments } = extractedData;
  const { days, timeSlots, batches, teachers, sectionsByBatchAndPrefix } = metadata;

  // Create workbook
  const workbook = new ExcelJS.Workbook();
  
  // Define styles
  const headerStyle = {
    font: { bold: true, size: 10 },
    alignment: { horizontal: 'center', vertical: 'middle' },
    border: {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' }
    },
    fill: {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    }
  };
  
  const dayStyle = {
    font: { bold: true, size: 11 },
    alignment: { horizontal: 'center', vertical: 'middle' },
    border: {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' }
    },
    fill: {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFCCFFCC' }
    }
  };
  
  const titleStyle = {
    font: { bold: true, size: 14 },
    alignment: { horizontal: 'center', vertical: 'middle' },
    fill: {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFCCCCFF' }
    }
  };
  
  const timeSlotStyle = {
    font: { bold: true, size: 10 },
    alignment: { horizontal: 'center', vertical: 'middle' },
    border: {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' }
    },
    fill: {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFFFD700' } // Gold color for time slots
    }
  };
  
  const cellStyle = {
    font: { size: 9 },
    alignment: { horizontal: 'center', vertical: 'top', wrapText: true },
    border: {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' }
    }
  };

  const labCellStyle = {
    ...cellStyle,
    fill: {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFFFF0CC' } // Light yellow for lab courses
    }
  };
  
  // 1. Create Batch-wise Sheet
  const batchSheet = workbook.addWorksheet('Batch-wise Routines');
  configureBatchSheet(batchSheet, assignments.byBatch, days, timeSlots, batches, titleStyle, dayStyle, headerStyle, timeSlotStyle, cellStyle, labCellStyle);
  
  // 2. Create Section-wise Sheet
  const sectionSheet = workbook.addWorksheet('Section-wise Routines');
  configureSectionSheet(sectionSheet, assignments.bySection, days, timeSlots, sectionsByBatchAndPrefix, titleStyle, dayStyle, headerStyle, timeSlotStyle, cellStyle, labCellStyle);
  
  // 3. Create Teacher-wise Sheet
  const teacherSheet = workbook.addWorksheet('Teacher-wise Routines');
  configureTeacherSheet(teacherSheet, assignments.byTeacher, days, timeSlots, teachers, titleStyle, dayStyle, headerStyle, timeSlotStyle, cellStyle, labCellStyle);
  
  // Set fixed column widths for better readability
  [batchSheet, sectionSheet, teacherSheet].forEach(sheet => {
    // Set first column (days) to a reasonable width
    sheet.getColumn(1).width = 15;
    
    // Set time slot columns to a wider width to accommodate content
    for (let i = 0; i < timeSlots.length; i++) {
      sheet.getColumn(i + 2).width = 25; // Wider columns for content
    }
  });

  // Save the workbook
  try {
    await workbook.xlsx.writeFile(outputPath);
    console.log(`Excel file successfully saved to ${outputPath}`);
  } catch (error) {
    console.error('Error writing Excel file:', error.message);
    process.exit(1);
  }
}

// Function to configure the batch-wise sheet with days in left column
function configureBatchSheet(worksheet, batchAssignments, days, timeSlots, batches, titleStyle, dayStyle, headerStyle, timeSlotStyle, cellStyle, labCellStyle) {
  let currentRow = 1;
  
  // Process each batch
  for (const batch of batches) {
    if (!batchAssignments[batch] || batchAssignments[batch].length === 0) {
      console.log(`Skipping batch ${batch} - no assignments found`);
      continue;
    }
    
    console.log(`Processing batch ${batch} with ${batchAssignments[batch].length} assignments`);
    
    // Add batch header
    worksheet.getRow(currentRow).height = 25;
    const batchCell = worksheet.getCell(currentRow, 1);
    batchCell.value = `Batch ${batch}`;
    batchCell.style = titleStyle;
    worksheet.mergeCells(currentRow, 1, currentRow, timeSlots.length + 1);
    currentRow++;
    
    // Add time slots in the header row
    worksheet.getRow(currentRow).height = 22;
    worksheet.getCell(currentRow, 1).value = 'Day / Time';
    worksheet.getCell(currentRow, 1).style = headerStyle;
    
    for (let i = 0; i < timeSlots.length; i++) {
      const cell = worksheet.getCell(currentRow, i + 2);
      cell.value = timeSlots[i];
      cell.style = timeSlotStyle;
    }
    currentRow++;
    
    // Process each day
    for (const day of days) {
      // Add day in the first column
      const dayCell = worksheet.getCell(currentRow, 1);
      dayCell.value = day;
      dayCell.style = dayStyle;
      
      // Initialize cell content for all time slots
      const slotContents = {};
      for (const slot of timeSlots) {
        slotContents[slot] = [];
      }
      
      // Find all assignments for this batch on this day
      const dayAssignments = batchAssignments[batch].filter(a => a.day === day);
      
      // Group assignments by time slot
      for (const assignment of dayAssignments) {
        if (slotContents[assignment.slot]) {
          slotContents[assignment.slot].push(assignment);
        }
      }
      
      // Process each time slot
      for (let slotIndex = 0; slotIndex < timeSlots.length; slotIndex++) {
        const slot = timeSlots[slotIndex];
        const colIndex = slotIndex + 2;
        const assignments = slotContents[slot] || [];
        
        // Create cell content
        if (assignments.length > 0) {
          let cellContent = '';
          let isLabClass = false;
          
          assignments.forEach((assignment, index) => {
            if (index > 0) cellContent += '\\n';
            
            // Safely extract section part
            let sectionPart = '';
            if (assignment.section && assignment.section.includes('_')) {
              const parts = assignment.section.split('_');
              if (parts.length > 1) {
                sectionPart = parts[1];
              } else {
                sectionPart = assignment.section;
              }
            } else if (assignment.section) {
              sectionPart = assignment.section;
            }
            
            // Format cell content with more spacing and clear labels
            cellContent += `${assignment.course} (${sectionPart})\\n\\nRoom: ${assignment.room}\\nTeacher: ${assignment.teacher}`;
            if (assignment.isLab) isLabClass = true;
          });
          
          const cell = worksheet.getCell(currentRow, colIndex);
          cell.value = cellContent.replace(/\\n/g, '\n');
          cell.style = isLabClass ? labCellStyle : cellStyle;
        } else {
          // Empty cell
          const cell = worksheet.getCell(currentRow, colIndex);
          cell.value = '';
          cell.style = cellStyle;
        }
      }
      
      // Adjust row height based on content
      let maxLines = 1;
      for (let i = 1; i <= timeSlots.length + 1; i++) {
        const cell = worksheet.getCell(currentRow, i);
        if (cell.value) {
          // Count the number of line breaks plus 1 for the first line
          const lineCount = (String(cell.value).match(/\n/g) || []).length + 1;
          maxLines = Math.max(maxLines, lineCount);
        }
      }
      
      // Set row height based on content (15 pixels per line plus some padding)
      const rowHeight = Math.max(20, Math.min(120, maxLines * 15 + 5));
      worksheet.getRow(currentRow).height = rowHeight;
      
      currentRow++;
    }
    
    // Add empty rows for separation between batches
    currentRow++;
    worksheet.getRow(currentRow).height = 10; // Thin separator row
    currentRow++;
  }
}

// Function to configure the section-wise sheet with days in left column
function configureSectionSheet(worksheet, sectionAssignments, days, timeSlots, sectionsByBatchAndPrefix, titleStyle, dayStyle, headerStyle, timeSlotStyle, cellStyle, labCellStyle) {
  let currentRow = 1;
  
  // Sort keys by batch and section
  const sortedKeys = Object.keys(sectionsByBatchAndPrefix).sort((a, b) => {
    const [batchA, sectionA] = a.split('_');
    const [batchB, sectionB] = b.split('_');
    
    if (batchA !== batchB) {
      return parseInt(batchA) - parseInt(batchB);
    }
    
    return sectionA.localeCompare(sectionB);
  });
  
  // Process each section group
  for (const key of sortedKeys) {
    const [batch, sectionPrefix] = key.split('_');
    const sections = sectionsByBatchAndPrefix[key];
    
    // Check if any sections in this group have assignments
    let hasAssignments = false;
    for (const section of sections) {
      if (sectionAssignments[section] && sectionAssignments[section].length > 0) {
        hasAssignments = true;
        break;
      }
    }
    
    if (!hasAssignments) {
      console.log(`Skipping section group ${key} - no assignments found`);
      continue;
    }
    
    console.log(`Processing section group ${key}`);
    
    // Add section header
    worksheet.getRow(currentRow).height = 25;
    const sectionCell = worksheet.getCell(currentRow, 1);
    sectionCell.value = `Batch ${batch} - Section ${sectionPrefix}`;
    sectionCell.style = titleStyle;
    worksheet.mergeCells(currentRow, 1, currentRow, timeSlots.length + 1);
    currentRow++;
    
    // Add time slots in the header row
    worksheet.getRow(currentRow).height = 22;
    worksheet.getCell(currentRow, 1).value = 'Day / Time';
    worksheet.getCell(currentRow, 1).style = headerStyle;
    
    for (let i = 0; i < timeSlots.length; i++) {
      const cell = worksheet.getCell(currentRow, i + 2);
      cell.value = timeSlots[i];
      cell.style = timeSlotStyle;
    }
    currentRow++;
    
    // Process each day
    for (const day of days) {
      // Add day in the first column
      const dayCell = worksheet.getCell(currentRow, 1);
      dayCell.value = day;
      dayCell.style = dayStyle;
      
      // Initialize cell content for all time slots
      const slotContents = {};
      for (const slot of timeSlots) {
        slotContents[slot] = [];
      }
      
      // Find all assignments for this section group on this day
      for (const section of sections) {
        if (!sectionAssignments[section]) continue;
        
        const dayAssignments = sectionAssignments[section].filter(a => a.day === day);
        
        // Group assignments by time slot
        for (const assignment of dayAssignments) {
          if (slotContents[assignment.slot]) {
            slotContents[assignment.slot].push(assignment);
          }
        }
      }
      
      // Process each time slot
      for (let slotIndex = 0; slotIndex < timeSlots.length; slotIndex++) {
        const slot = timeSlots[slotIndex];
        const colIndex = slotIndex + 2;
        const assignments = slotContents[slot] || [];
        
        // Create cell content
        if (assignments.length > 0) {
          let cellContent = '';
          let isLabClass = false;
          
          assignments.forEach((assignment, index) => {
            if (index > 0) cellContent += '\\n';
            
            // Safely extract section part
            let sectionPart = '';
            if (assignment.section && assignment.section.includes('_')) {
              const parts = assignment.section.split('_');
              if (parts.length > 1) {
                sectionPart = parts[1];
              } else {
                sectionPart = assignment.section;
              }
            } else if (assignment.section) {
              sectionPart = assignment.section;
            }
            
            // Format cell content with more spacing and clear labels
            cellContent += `${assignment.course} (${sectionPart})\\n\\nRoom: ${assignment.room}\\nTeacher: ${assignment.teacher}`;
            if (assignment.isLab) isLabClass = true;
          });
          
          const cell = worksheet.getCell(currentRow, colIndex);
          cell.value = cellContent.replace(/\\n/g, '\n');
          cell.style = isLabClass ? labCellStyle : cellStyle;
        } else {
          // Empty cell
          const cell = worksheet.getCell(currentRow, colIndex);
          cell.value = '';
          cell.style = cellStyle;
        }
      }
      
      // Adjust row height based on content
      let maxLines = 1;
      for (let i = 1; i <= timeSlots.length + 1; i++) {
        const cell = worksheet.getCell(currentRow, i);
        if (cell.value) {
          // Count the number of line breaks plus 1 for the first line
          const lineCount = (String(cell.value).match(/\n/g) || []).length + 1;
          maxLines = Math.max(maxLines, lineCount);
        }
      }
      
      // Set row height based on content (15 pixels per line plus some padding)
      const rowHeight = Math.max(20, Math.min(120, maxLines * 15 + 5));
      worksheet.getRow(currentRow).height = rowHeight;
      
      currentRow++;
    }
    
    // Add empty rows for separation between sections
    currentRow++;
    worksheet.getRow(currentRow).height = 10; // Thin separator row
    currentRow++;
  }
}

// Function to configure the teacher-wise sheet with days in left column
function configureTeacherSheet(worksheet, teacherAssignments, days, timeSlots, teachers, titleStyle, dayStyle, headerStyle, timeSlotStyle, cellStyle, labCellStyle) {
  let currentRow = 1;
  
  // Process each teacher
  for (const teacher of teachers) {
    // Skip null teachers or teachers with no assignments
    if (!teacher || !teacherAssignments[teacher] || teacherAssignments[teacher].length === 0) {
      console.log(`Skipping teacher ${teacher} - no assignments found`);
      continue;
    }
    
    console.log(`Processing teacher ${teacher} with ${teacherAssignments[teacher].length} assignments`);
    
    // Add teacher header
    worksheet.getRow(currentRow).height = 25;
    const teacherCell = worksheet.getCell(currentRow, 1);
    teacherCell.value = `Teacher: ${teacher}`;
    teacherCell.style = titleStyle;
    worksheet.mergeCells(currentRow, 1, currentRow, timeSlots.length + 1);
    currentRow++;
    
    // Add time slots in the header row
    worksheet.getRow(currentRow).height = 22;
    worksheet.getCell(currentRow, 1).value = 'Day / Time';
    worksheet.getCell(currentRow, 1).style = headerStyle;
    
    for (let i = 0; i < timeSlots.length; i++) {
      const cell = worksheet.getCell(currentRow, i + 2);
      cell.value = timeSlots[i];
      cell.style = timeSlotStyle;
    }
    currentRow++;
    
    // Process each day
    for (const day of days) {
      // Add day in the first column
      const dayCell = worksheet.getCell(currentRow, 1);
      dayCell.value = day;
      dayCell.style = dayStyle;
      
      // Initialize cell content for all time slots
      const slotContents = {};
      for (const slot of timeSlots) {
        slotContents[slot] = [];
      }
      
      // Find all assignments for this teacher on this day
      const dayAssignments = teacherAssignments[teacher].filter(a => a.day === day);
      
      // Group assignments by time slot
      for (const assignment of dayAssignments) {
        if (slotContents[assignment.slot]) {
          slotContents[assignment.slot].push(assignment);
        }
      }
      
      // Process each time slot
      for (let slotIndex = 0; slotIndex < timeSlots.length; slotIndex++) {
        const slot = timeSlots[slotIndex];
        const colIndex = slotIndex + 2;
        const assignments = slotContents[slot] || [];
        
        // Create cell content
        if (assignments.length > 0) {
          let cellContent = '';
          let isLabClass = false;
          
          assignments.forEach((assignment, index) => {
            if (index > 0) cellContent += '\\n';
            
            // Format cell content with more spacing and clear labels
            cellContent += `${assignment.course}\\n\\nSection: ${assignment.section}\\nRoom: ${assignment.room}`;
            if (assignment.isLab) isLabClass = true;
          });
          
          const cell = worksheet.getCell(currentRow, colIndex);
          cell.value = cellContent.replace(/\\n/g, '\n');
          cell.style = isLabClass ? labCellStyle : cellStyle;
        } else {
          // Empty cell
          const cell = worksheet.getCell(currentRow, colIndex);
          cell.value = '';
          cell.style = cellStyle;
        }
      }
      
      // Adjust row height based on content
      let maxLines = 1;
      for (let i = 1; i <= timeSlots.length + 1; i++) {
        const cell = worksheet.getCell(currentRow, i);
        if (cell.value) {
          // Count the number of line breaks plus 1 for the first line
          const lineCount = (String(cell.value).match(/\n/g) || []).length + 1;
          maxLines = Math.max(maxLines, lineCount);
        }
      }
      
      // Set row height based on content (15 pixels per line plus some padding)
      const rowHeight = Math.max(20, Math.min(120, maxLines * 15 + 5));
      worksheet.getRow(currentRow).height = rowHeight;
      
      currentRow++;
    }
    
    // Add empty rows for separation between teachers
    currentRow++;
    worksheet.getRow(currentRow).height = 10; // Thin separator row
    currentRow++;
  }
}

// Main function
async function main() {
  // Load the extracted data
  const extractedData = loadExtractedData('extracted_routine_data.json');

  // Create and save the Excel file
  await createExcelFile(extractedData, 'weekly_routine_complete.xlsx');
}

// Execute
main().catch(error => {
  console.error('Error in main:', error.message);
  process.exit(1);
});
