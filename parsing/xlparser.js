const fs = require('fs');
const xlsx = require('xlsx');
const path = require('path');

const filePath = '/home/<USER>/Desktop/diu_routine_generator/template/routine_summer_2025_new.xlsx';
const outputJsonPath = 'class_routine_summer_2025_new2.json';

// Read the Excel file
const workbook = xlsx.readFile(filePath);

// Convert all sheets to JSON
const allData = {};
workbook.SheetNames.forEach(sheetName => {
    const sheet = workbook.Sheets[sheetName];
    const data = xlsx.utils.sheet_to_json(sheet, { defval: "" }); // defval to handle nulls
    allData[sheetName] = data;
});

// Write to JSON file
fs.writeFileSync(outputJsonPath, JSON.stringify(allData, null, 4), 'utf8');

console.log(`Successfully converted to ${outputJsonPath}`);
