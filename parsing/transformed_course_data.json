{"batches": [{"code": "L-1, T-1 (<PERSON><PERSON><PERSON>68) (New Curriculum-3, Effective from Spring 2023)", "total_sections": 20, "teacher_credits": {"student_credits": 12, "teacher_credits": 12}, "courses": [{"code": "CSE112", "title": "Computer Fundamentals", "student_credits": 3, "teacher_credits": 3, "instructors": {"68_A": "FZA", "68_B": "ACC", "68_C": "ACC", "68_D": "LR", "68_E": "FNK", "68_F": "FNK", "68_G": "PPC", "68_H": "LR", "68_I": "MA", "68_J": "IJM", "68_K": "MHD", "68_L": "TAB", "68_M": "MHS", "68_N": "HMK", "68_O": "MUH", "68_P": "MB", "68_Q": "MB", "68_R": "PPC", "68_S": "MUH", "68_T": "MHS"}}, {"code": "ENG101", "title": "Basic Functional English and English Spoken", "student_credits": 3, "teacher_credits": 3, "instructors": {"68_A": "AJS", "68_B": "SMH", "68_C": "SMH", "68_D": "AJS", "68_E": "AJS", "68_F": "SSA", "68_G": "SMSH", "68_H": "SMSH", "68_I": "SSA", "68_J": "SSA", "68_K": "ANR", "68_L": "ASA", "68_M": "ASA", "68_N": "ANR", "68_O": "ANR", "68_P": "ANR", "68_Q": "ANR", "68_R": "SMSH", "68_S": "SMSH", "68_T": "SIS"}}, {"code": "MAT101", "title": "Mathematics - I", "student_credits": 3, "teacher_credits": 3, "instructors": {"68_A": "MPL", "68_B": "MSH", "68_C": "AST", "68_D": "MPL", "68_E": "MPL", "68_F": "MRR", "68_G": "AST", "68_H": "MRR", "68_I": "AST", "68_J": "AST", "68_K": "MSH", "68_L": "MSH", "68_M": "SP", "68_N": "MSH", "68_O": "AMN", "68_P": "SP", "68_Q": "MSH", "68_R": "AMN", "68_S": "AMN", "68_T": "MRR"}}, {"code": "CSE115", "title": "Introduction to Biology and Chemistry for Computation", "student_credits": 3, "teacher_credits": 3, "instructors": {"68_A": "FNK", "68_B": "FNK", "68_C": "ABA", "68_D": "AUR", "68_E": "ABA", "68_F": "ABA", "68_G": "MSM", "68_H": "SNK", "68_I": "FTJ", "68_J": "PDS", "68_K": "AUA", "68_L": "FTJ", "68_M": "PDS", "68_N": "MUR", "68_O": "ZMK", "68_P": "NIB", "68_Q": "NIB", "68_R": "AUR", "68_S": "MSM", "68_T": "ZMK"}}]}, {"code": "L-1, T-2 (<PERSON><PERSON><PERSON>67) (New Curriculum-3, Effective from Spring 2023)", "total_sections": 18, "teacher_credits": {"student_credits": 15, "teacher_credits": 15}, "courses": [{"code": "ENG102", "title": "Writing and Comprehension", "student_credits": 3, "teacher_credits": 3, "instructors": {"67_A": "AJS", "67_B": "SMH", "67_C": "SMH", "67_D": "AJS", "67_E": "SMH", "67_F": "SSA", "67_G": "TAA", "67_H": "TAA", "67_I": "SSA", "67_J": "SIS", "67_K": "MFA", "67_L": "ASA", "67_M": "ASA", "67_N": "MFA", "67_O": "TAA", "67_P": "TAA", "67_Q": "MFA", "67_R": "MFA"}}, {"code": "MAT102", "title": "Mathematics-II: Calculus, Complex Variables and Linear Algebra", "student_credits": 3, "teacher_credits": 3, "instructors": {"67_A": "SSL", "67_B": "SP", "67_C": "SP", "67_D": "SSL", "67_E": "SSL", "67_F": "SP", "67_G": "SSL", "67_H": "AKC", "67_I": "AKC", "67_J": "SSL", "67_K": "MPL", "67_L": "AKC", "67_M": "AKC", "67_N": "MPL", "67_O": "AKC", "67_P": "MRR", "67_Q": "ZT", "67_R": "MNM"}}, {"code": "PHY102", "title": "Physics - II", "student_credits": 3, "teacher_credits": 3, "instructors": {"67_A": "AKA", "67_B": "MAK", "67_C": "MAK", "67_D": "SSK", "67_E": "AAS", "67_F": "AAS", "67_G": "SSK", "67_H": "AAR", "67_I": "PNT_2", "67_J": "AEE", "67_K": "RIM", "67_L": "MAM", "67_M": "AEE", "67_N": "RIM", "67_O": "AAR", "67_P": "AHT", "67_Q": "MAM", "67_R": "SHN"}}, {"code": "PHY103", "title": "Physics - II Lab", "student_credits": 1.5, "teacher_credits": 1.5, "instructors": {"67_A": "SHN", "67_B": "AHT", "67_C": "PNT_2", "67_D": "SSK", "67_E": "AAS", "67_F": "AAS", "67_G": "SSK", "67_H": "AAR", "67_I": "PNT_2", "67_J": "AEE", "67_K": "RIM", "67_L": "MAM", "67_M": "AEE", "67_N": "RIM", "67_O": "AAR", "67_P": "AHT", "67_Q": "MAM", "67_R": "SHN"}}, {"code": "CSE123", "title": "Data Structure", "student_credits": 3, "teacher_credits": 3, "instructors": {"67_A": "SAH", "67_B": "AAK", "67_C": "ABA", "67_D": "SRH", "67_E": "SRH", "67_F": "MMR", "67_G": "AAK", "67_H": "SAJ", "67_I": "HH", "67_J": "HH", "67_K": "THT", "67_L": "MJZ", "67_M": "MJZ", "67_N": "JLA", "67_O": "SIP", "67_P": "MHS", "67_Q": "RKR", "67_R": "MSM"}}, {"code": "CSE124", "title": "Data Structure Lab", "student_credits": 1.5, "teacher_credits": 1.5, "instructors": {"67_A": "SAH", "67_B": "AAK", "67_C": "ABA", "67_D": "SRH", "67_E": "MIR", "67_F": "MMR", "67_G": "AAK", "67_H": "RKR", "67_I": "HH", "67_J": "HH", "67_K": "THT", "67_L": "MJZ", "67_M": "MJZ", "67_N": "JLA", "67_O": "SIP", "67_P": "MHS", "67_Q": "RKR", "67_R": "MSM"}}]}, {"code": "L-2, T-1 (<PERSON><PERSON><PERSON>66) (New Curriculum-3, Effective from Spring 2023)", "total_sections": 18, "teacher_credits": {"student_credits": 13.5, "teacher_credits": 13.5}, "courses": [{"code": "MAT211", "title": "Engineering Mathematics", "student_credits": 3, "teacher_credits": 3, "instructors": {"66_A": "BCD", "66_B": "BCD", "66_C": "NRM", "66_D": "NRM", "66_E": "MNM", "66_F": "MNM", "66_G": "MYA", "66_H": "NRM", "66_I": "MYA", "66_J": "NDS", "66_K": "ZT", "66_L": "NDS", "66_M": "NDS", "66_N": "NDS", "66_O": "BCD", "66_P": "NRM", "66_Q": "MYA", "66_R": "NRM"}}, {"code": "CSE212", "title": "Discrete Mathematics", "student_credits": 3, "teacher_credits": 3, "instructors": {"66_A": "AGT", "66_B": "DMR", "66_C": "SR", "66_D": "SR", "66_E": "DMR", "66_F": "DMR", "66_G": "AHN", "66_H": "SR", "66_I": "MHIM", "66_J": "AHN", "66_K": "AHN", "66_L": "AGT", "66_M": "RKR", "66_N": "RKR", "66_O": "MMI", "66_P": "MMI", "66_Q": "SIP", "66_R": "ZAF"}}, {"code": "CSE213", "title": "Algorithms", "student_credits": 3, "teacher_credits": 3, "instructors": {"66_A": "MHN", "66_B": "TAR", "66_C": "SI", "66_D": "SMAH", "66_E": "SI", "66_F": "FAF", "66_G": "AAKA", "66_H": "SI", "66_I": "SS", "66_J": "SI", "66_K": "FAF", "66_L": "SS", "66_M": "SNK", "66_N": "TAR", "66_O": "MHN", "66_P": "FAF", "66_Q": "MJA", "66_R": "MUH"}}, {"code": "CSE214", "title": "Algorithms Lab", "student_credits": 1.5, "teacher_credits": 1.5, "instructors": {"66_A": "MHN", "66_B": "TAR", "66_C": "SI", "66_D": "SNK", "66_E": "MUH", "66_F": "FAF", "66_G": "AAKA", "66_H": "SIP", "66_I": "SS", "66_J": "SAJ", "66_K": "FAF", "66_L": "SS", "66_M": "SNK", "66_N": "TAR", "66_O": "MHN", "66_P": "SAJ", "66_Q": "FAA", "66_R": "MUH"}}, {"code": "BNS101", "title": "Bangladesh Studies \n(History of Independence and Contemporary Issues)", "student_credits": 3, "teacher_credits": 3, "instructors": {"66_A": "RAM", "66_B": "RAM", "66_C": "RAM", "66_D": "SMN", "66_E": "SMN", "66_F": "SMN", "66_G": "SMN", "66_H": "MAA", "66_I": "MAA", "66_J": "MAA", "66_K": "MMC", "66_L": "MMC", "66_M": "MMC", "66_N": "MAA", "66_O": "SHS", "66_P": "SHS", "66_Q": "MMC", "66_R": "SHS"}}]}, {"code": "L-2, T-2 (<PERSON><PERSON><PERSON>65) (New Curriculum-3, Effective from Spring 2023", "total_sections": 16, "teacher_credits": {"student_credits": 15, "teacher_credits": 15}, "courses": [{"code": "CSE221", "title": "Object Oriented Programming", "student_credits": 3, "teacher_credits": 3, "instructors": {"65_A": "SAH", "65_B": "NIB", "65_C": "AUA", "65_D": "JAR", "65_E": "SGP", "65_F": "MFZ", "65_G": "SGP", "65_H": "UA", "65_I": "HJ", "65_J": "SH", "65_K": "UA", "65_L": "HJ", "65_M": "AHS", "65_N": "AHS", "65_O": "MFZ", "65_P": "JAR"}}, {"code": "CSE222", "title": "Object Oriented Programming Lab", "student_credits": 1.5, "teacher_credits": 1.5, "instructors": {"65_A": "SAH", "65_B": "NIB", "65_C": "AUA", "65_D": "SZ", "65_E": "SGP", "65_F": "AUA", "65_G": "MTH", "65_H": "UA", "65_I": "HJ", "65_J": "MFZ", "65_K": "UA", "65_L": "HJ", "65_M": "AHS", "65_N": "AHS", "65_O": "MFZ", "65_P": "JAR"}}, {"code": "CSE223", "title": "Digital Logic Design", "student_credits": 3, "teacher_credits": 3, "instructors": {"65_A": "AKM", "65_B": "AKM", "65_C": "RHK", "65_D": "SHD", "65_E": "SHD", "65_F": "MAI", "65_G": "MAI", "65_H": "BM", "65_I": "SMA", "65_J": "SMA", "65_K": "ARS", "65_L": "RHK", "65_M": "MRIS", "65_N": "MRIS", "65_O": "GR", "65_P": "ZMK"}}, {"code": "CSE224", "title": "Digital Logic Design Lab", "student_credits": 1.5, "teacher_credits": 1.5, "instructors": {"65_A": "AKM", "65_B": "AKM", "65_C": "ZS", "65_D": "SHD", "65_E": "SHD", "65_F": "MAI", "65_G": "MAI", "65_H": "SHR", "65_I": "SMA", "65_J": "SMA", "65_K": "ZS", "65_L": "RHK", "65_M": "MRIS", "65_N": "AGT", "65_O": "GR", "65_P": "ZMK"}}, {"code": "CSE225", "title": "Data Communication", "student_credits": 3, "teacher_credits": 3, "instructors": {"65_A": "TI", "65_B": "MFH", "65_C": "MFH", "65_D": "MFH", "65_E": "TI", "65_F": "AM", "65_G": "AM", "65_H": "AM", "65_I": "JLA", "65_J": "MSS", "65_K": "MSS", "65_L": "JLA", "65_M": "ZS", "65_N": "ZS", "65_O": "AAKA", "65_P": "AAKA"}}, {"code": "CSE228", "title": "Theory of Computation", "student_credits": 3, "teacher_credits": 3, "instructors": {"65_A": "TRA", "65_B": "SAJ", "65_C": "ZNM", "65_D": "MAH", "65_E": "MAH", "65_F": "ALM", "65_G": "FMA", "65_H": "FMA", "65_I": "TRA", "65_J": "ADK", "65_K": "TRA", "65_L": "MAH", "65_M": "HHP", "65_N": "ALM", "65_O": "TDR", "65_P": "TDR"}}]}, {"code": "L-3, T-1 (<PERSON>ch-64) (New Curriculum-3, Effective from Spring 2023", "total_sections": 15, "teacher_credits": {"student_credits": 12, "teacher_credits": 12}, "courses": [{"code": "CSE311", "title": "Database Management System", "student_credits": 3, "teacher_credits": 3, "instructors": {"64_A": "NK", "64_B": "NK", "64_C": "MSK", "64_D": "SH", "64_E": "MUM", "64_F": "MJA", "64_G": "MUM", "64_H": "MB", "64_I": "MJA", "64_J": "TFS", "64_K": "TAB", "64_L": "TFS", "64_M": "TFS", "64_N": "SH", "64_O": "SAS"}}, {"code": "CSE312", "title": "Database Management System Lab", "student_credits": 1.5, "teacher_credits": 1.5, "instructors": {"64_A": "NK", "64_B": "NK", "64_C": "MSK", "64_D": "SH", "64_E": "MUM", "64_F": "MJA", "64_G": "MUM", "64_H": "MB", "64_I": "MSK", "64_J": "TFS", "64_K": "TAB", "64_L": "TAB", "64_M": "MHS", "64_N": "SAS", "64_O": "SAS"}}, {"code": "CSE321", "title": "Computer Networks", "student_credits": 3, "teacher_credits": 3, "instructors": {"64_A": "PDS", "64_B": "IJM", "64_C": "FTJ", "64_D": "CSA", "64_E": "MSI", "64_F": "NHA", "64_G": "ARS", "64_H": "ARS", "64_I": "IJM", "64_J": "MIN", "64_K": "ZZ", "64_L": "ZZ", "64_M": "MSI", "64_N": "TI", "64_O": "NTS"}}, {"code": "CSE322", "title": "Computer Networks Lab", "student_credits": 1.5, "teacher_credits": 1.5, "instructors": {"64_A": "PDS", "64_B": "IJM", "64_C": "FTJ", "64_D": "CSA", "64_E": "CSA", "64_F": "PDS", "64_G": "ARS", "64_H": "ARS", "64_I": "IJM", "64_J": "MIN", "64_K": "ZZ", "64_L": "ZZ", "64_M": "MSI", "64_N": "TI", "64_O": "NTS"}}, {"code": "ACT327", "title": "Financial and Managerial Accounting", "student_credits": 3, "teacher_credits": 3, "instructors": {"64_A": "SMR", "64_B": "SMR", "64_C": "ARN", "64_D": "ARN", "64_E": "DOF", "64_F": "DOF", "64_G": "ARN", "64_H": "SMR", "64_I": "SMR", "64_J": "SAS", "64_K": "JM", "64_L": "JM", "64_M": "SAS", "64_N": "ARN", "64_O": "SAS"}}]}, {"code": "L-3, T-2 (<PERSON><PERSON><PERSON>63) (New Curriculum-2, Effective from Spring 2022)", "total_sections": 15, "teacher_credits": {"student_credits": 14, "teacher_credits": 14}, "courses": [{"code": "CSE321", "title": "System Analysis and Design", "student_credits": 3, "teacher_credits": 3, "instructors": {"63_A": "JHJ", "63_B": "ACC", "63_C": "JHJ", "63_D": "MIS", "63_E": "FFZ", "63_F": "RAK", "63_G": "NS", "63_H": "NS", "63_I": "NS", "63_J": "MIN", "63_K": "TAS", "63_L": "MIN", "63_M": "TAS", "63_N": "DS", "63_O": "AIR"}}, {"code": "CSE323", "title": "Operating Systems", "student_credits": 2, "teacher_credits": 2, "instructors": {"63_A": "NJO", "63_B": "ADK", "63_C": "TAK", "63_D": "TAK", "63_E": "IJN", "63_F": "IJN", "63_G": "ADK", "63_H": "RAK", "63_I": "DS", "63_J": "RAK", "63_K": "DS", "63_L": "FFZ", "63_M": "DS", "63_N": "FFZ", "63_O": "JIA"}}, {"code": "CSE324", "title": "Operating Systems Lab", "student_credits": 2, "teacher_credits": 2, "instructors": {"63_A": "NJO", "63_B": "ADK", "63_C": "TAK", "63_D": "TAK", "63_E": "IJN", "63_F": "IJN", "63_G": "ADK", "63_H": "RAK", "63_I": "DS", "63_J": "RAK", "63_K": "FLA", "63_L": "FFZ", "63_M": "NJO", "63_N": "FFZ", "63_O": "JIA"}}, {"code": "CSE431", "title": "Social and Professional Issues in Computing", "student_credits": 3, "teacher_credits": 3, "instructors": {"63_A": "MIR", "63_B": "CSA", "63_C": "HMK", "63_D": "MIR", "63_E": "MAR", "63_F": "MIR", "63_G": "MMI", "63_H": "RIA", "63_I": "GR", "63_J": "MMB", "63_K": "NJO", "63_L": "GR", "63_M": "MMB", "63_N": "RIA", "63_O": "MHIM"}}, {"code": "CSE233", "title": "Embedded Systems and IoT", "student_credits": 2, "teacher_credits": 2, "instructors": {"63_A": "MHK", "63_B": "FZA", "63_C": "MHK", "63_D": "MHK", "63_E": "TARA", "63_F": "TARA", "63_G": "SMC", "63_H": "BM", "63_I": "BM", "63_J": "SMC", "63_K": "MHI", "63_L": "MHI", "63_M": "MTF", "63_N": "MTF", "63_O": "SMC"}}, {"code": "CSE234", "title": "Embedded Systems and IoT Lab", "student_credits": 2, "teacher_credits": 2, "instructors": {"63_A": "MHK", "63_B": "FZA", "63_C": "MHK", "63_D": "MRIS", "63_E": "TARA", "63_F": "TARA", "63_G": "SMC", "63_H": "BM", "63_I": "BM", "63_J": "SMC", "63_K": "MHI", "63_L": "MHI", "63_M": "MTF", "63_N": "MTF", "63_O": "EHL"}}]}, {"code": "L-3, T-2 (<PERSON><PERSON><PERSON>62) (New Curriculum-2, Effective from Spring 2022)", "total_sections": 8, "teacher_credits": {"student_credits": 13, "teacher_credits": 13}, "courses": [{"code": "CSE321", "title": "System Analysis and Design", "student_credits": 3, "teacher_credits": 3, "instructors": {"62_A": "MSJ", "62_B": "MSJ", "62_C": "MM", "62_D": "MM", "62_E": "MIZ", "62_F": "MMRN", "62_G": "IJN", "62_H": "AIR"}}, {"code": "CSE325", "title": "Data Mining and Machine Learning", "student_credits": 3, "teacher_credits": 3, "instructors": {"62_A": "DMAK", "62_B": "SN", "62_C": "FH", "62_D": "ZH", "62_E": "ZH", "62_F": "ZH", "62_G": "DMAK", "62_H": "AIR"}}, {"code": "CSE326", "title": "Data Mining and Machine Learning Lab", "student_credits": 1, "teacher_credits": 1, "instructors": {"62_A": "SKN", "62_B": "SN", "62_C": "FH", "62_D": "MSS", "62_E": "AHAK", "62_F": "SN", "62_G": "SKN", "62_H": "AIR"}}, {"code": "CSE333", "title": "Software Engineering", "student_credits": 3, "teacher_credits": 3, "instructors": {"62_A": "SNA", "62_B": "ZS", "62_C": "AS", "62_D": "AS", "62_E": "SNA", "62_F": "AS", "62_G": "SAL", "62_H": "NTS"}}, {"code": "CSE423", "title": "Information Security    (Elective I)", "student_credits": 3, "teacher_credits": 3, "instructors": {"62_A": "LR", "62_B": "DAA", "62_C": "MSK", "62_D": "STA", "62_E": "MSK", "62_F": "LR", "62_G": "DAA", "62_H": "STA"}}]}, {"code": "L-4,T-1 (<PERSON><PERSON><PERSON>61) (New Curriculum-2, Effective from Spring 2022)", "total_sections": 22, "teacher_credits": {"student_credits": 14, "teacher_credits": 14}, "courses": [{"code": "CSE331", "title": "Compiler Design", "student_credits": 3, "teacher_credits": 3, "instructors": {"61_A": "SHR", "61_B": "TDR", "61_C": "SMTS", "61_D": "SMTS", "61_E": "MUR", "61_F": "SMTS", "61_G": "SMTS", "61_H": "MMB", "61_I": "MUR", "61_J": "MALH", "61_K": "SHR", "61_L": "ROZ", "61_M": "MAR", "61_N": "ROZ", "61_O": "MAR", "61_P": "ZAF", "61_Q": "ALE", "61_R": "ZAF", "61_S": "ALE", "61_T": "UH", "61_U": "UH", "61_V": "MUR"}}, {"code": "CSE332", "title": "Compiler Design Lab", "student_credits": 1, "teacher_credits": 1, "instructors": {"61_A": "SHR", "61_B": "TDR", "61_C": "MMB", "61_D": "MALH", "61_E": "MUR", "61_F": "AGT", "61_G": "MAIT", "61_H": "MMB", "61_I": "ZNM", "61_J": "MALH", "61_K": "SHR", "61_L": "ROZ", "61_M": "MAR", "61_N": "ROZ", "61_O": "MAR", "61_P": "ZAF", "61_Q": "ALE", "61_R": "ZAF", "61_S": "ALE", "61_T": "UH", "61_U": "UH", "61_V": "HMK"}}, {"code": "CSE335", "title": "Computer Architecture and Organization", "student_credits": 3, "teacher_credits": 3, "instructors": {"61_A": "MAIT", "61_B": "UH", "61_C": "THT", "61_D": "SNA", "61_E": "SZ", "61_F": "SZ", "61_G": "SNA", "61_H": "THT", "61_I": "THT", "61_J": "NSL", "61_K": "ROZ", "61_L": "DRAR", "61_M": "DRAR", "61_N": "SEA", "61_O": "NAE", "61_P": "AAA", "61_Q": "SZ", "61_R": "NSL", "61_S": "SZ", "61_T": "FFN", "61_U": "FFN", "61_V": "MALH"}}, {"code": "CSE411", "title": "Artificial Intelligence", "student_credits": 3, "teacher_credits": 3, "instructors": {"61_A": "NNM", "61_B": "AHAK", "61_C": "AAK", "61_D": "SAK", "61_E": "MTA", "61_F": "AHN", "61_G": "AHN", "61_H": "MIS", "61_I": "MIS", "61_J": "MMRN", "61_K": "MMRN", "61_L": "ALM", "61_M": "AHAK", "61_N": "SN", "61_O": "SAK", "61_P": "MA", "61_Q": "MA", "61_R": "SN", "61_S": "NT-12", "61_T": "MSS", "61_U": "NT-12", "61_V": "AAM"}}, {"code": "CSE412", "title": "Artificial Intelligence Lab", "student_credits": 1, "teacher_credits": 1, "instructors": {"61_A": "NNM", "61_B": "AHAK", "61_C": "NNM", "61_D": "SAK", "61_E": "JAR", "61_F": "AUR", "61_G": "MSS", "61_H": "MIS", "61_I": "MIS", "61_J": "MMRN", "61_K": "MMRN", "61_L": "ALM", "61_M": "SKN", "61_N": "TDR", "61_O": "SAK", "61_P": "MA", "61_Q": "MA", "61_R": "MSM", "61_S": "NT-12", "61_T": "MSS", "61_U": "NT-12", "61_V": "MALH"}}, {"code": "CSE413", "title": "Mobile Application Design", "student_credits": 1, "teacher_credits": 1, "instructors": {"61_A": "MIZ", "61_B": "MIZ", "61_C": "SEA", "61_D": "NAE", "61_E": "NAE", "61_F": "ZNM", "61_G": "SEA", "61_H": "FFN", "61_I": "AAA", "61_J": "HHP", "61_K": "AAA", "61_L": "HHP", "61_M": "SAL", "61_N": "NHA", "61_O": "SAL", "61_P": "NHA", "61_Q": "NSL", "61_R": "FFN", "61_S": "JHJ", "61_T": "BK", "61_U": "JHJ", "61_V": "FMA"}}, {"code": "CSE414", "title": "Mobile Application Design Lab", "student_credits": 2, "teacher_credits": 2, "instructors": {"61_A": "MIZ", "61_B": "MIZ", "61_C": "SEA", "61_D": "NAE", "61_E": "NAE", "61_F": "ZNM", "61_G": "SEA", "61_H": "FFN", "61_I": "AAA", "61_J": "HHP", "61_K": "AAA", "61_L": "HHP", "61_M": "SAL", "61_N": "NHA", "61_O": "SAL", "61_P": "NHA", "61_Q": "NSL", "61_R": "FFN", "61_S": "BK", "61_T": "NSL", "61_U": "JHJ", "61_V": "FMA"}}]}, {"code": "L-4,T-2 (<PERSON><PERSON><PERSON>60) (New Curriculum-1, Effective from Spring 2019)", "total_sections": 6, "teacher_credits": {"student_credits": 12, "teacher_credits": 12}, "courses": [{"code": "CSE421", "title": "Computer Graphics", "student_credits": 1, "teacher_credits": 1, "instructors": {"60_A": "STA", "60_B": "TRA", "60_C": "STA", "60_D": "DRAR", "60_E": "DRAR", "60_F": "PPC"}}, {"code": "CSE422", "title": "Computer Graphics Lab", "student_credits": 2, "teacher_credits": 2, "instructors": {"60_A": "STA", "60_B": "TRA", "60_C": "MAH", "60_D": "DRAR", "60_E": "DRAR", "60_F": "PPC"}}, {"code": "CSE414", "title": "Web Engineering", "student_credits": 1, "teacher_credits": 1, "instructors": {"60_A": "MAIT", "60_B": "MHD", "60_C": "MHD", "60_D": "MSJ", "60_E": "MAIT", "60_F": "RIA"}}, {"code": "CSE415", "title": "Web Engineering Lab", "student_credits": 2, "teacher_credits": 2, "instructors": {"60_A": "MAIT", "60_B": "MHD", "60_C": "RIA", "60_D": "MSJ", "60_E": "MSJ", "60_F": "RIA"}}, {"code": "CSE445", "title": "Natural Language Processing    (Elective-1)", "student_credits": 3, "teacher_credits": 3, "instructors": {"60_A": "SR", "60_B": "AHAK", "60_C": "FH", "60_D": "FH", "60_E": "SAK", "60_F": "FH"}}, {"code": "CSE499", "title": "Project / Internship (Phase I, to be completed in Level-4 Term-3", "student_credits": 3, "teacher_credits": 3, "instructors": {}}]}, {"code": "L-4,T-2 (<PERSON><PERSON><PERSON>59) (New Curriculum-1, Effective from Spring 2019)", "total_sections": 4, "teacher_credits": {"student_credits": 10, "teacher_credits": 10}, "courses": [{"code": "CSE498", "title": "Social and Professional Issues in Computing", "student_credits": 3, "teacher_credits": 3, "instructors": {"59_A": "MMI", "59_B": "TAK", "59_C": "NTS", "59_D": "MMI"}}, {"code": "CSE427", "title": "Digital Image Processing  (Elective-II) ", "student_credits": 3, "teacher_credits": 3, "instructors": {"59_A": "NS", "59_B": "MUM", "59_C": "AUR", "59_D": "MJZ"}}, {"code": "CSE499", "title": "Project / Internship (Phase II, continued from Level 4 Term 2)", "student_credits": 4, "teacher_credits": 4, "instructors": {}}]}], "teachers": ["AAA", "AAK", "AAKA", "AAM", "AAR", "AAS", "ABA", "ACC", "ADK", "AEE", "AGT", "AHAK", "AHN", "AHS", "AHT", "AIR", "AJS", "AKA", "AKC", "AKM", "ALE", "ALM", "AM", "AMN", "ANR", "ARN", "ARS", "AS", "ASA", "AST", "AUA", "AUR", "BCD", "BK", "BM", "CSA", "DAA", "DMAK", "DMR", "DOF", "DRAR", "DS", "EHL", "FAA", "FAF", "FFN", "FFZ", "FH", "FLA", "FMA", "FNK", "FTJ", "FZA", "GR", "HH", "HHP", "HJ", "HMK", "IJM", "IJN", "JAR", "JHJ", "JIA", "JLA", "JM", "LR", "MA", "MAA", "MAH", "MAI", "MAIT", "MAK", "MALH", "MAM", "MAR", "MB", "MFA", "MFH", "MFZ", "MHD", "MHI", "MHIM", "MHK", "MHN", "MHS", "MIN", "MIR", "MIS", "MIZ", "MJA", "MJZ", "MM", "MMB", "MMC", "MMI", "MMR", "MMRN", "MNM", "MPL", "MRIS", "MRR", "MSH", "MSI", "MSJ", "MSK", "MSM", "MSS", "MTA", "MTF", "MTH", "MUH", "MUM", "MUR", "MYA", "NAE", "NDS", "NHA", "NIB", "NJO", "NK", "NNM", "NRM", "NS", "NSL", "NT-12", "NTS", "PDS", "PNT_2", "PPC", "RAK", "RAM", "RHK", "RIA", "RIM", "RKR", "ROZ", "SAH", "SAJ", "SAK", "SAL", "SAS", "SEA", "SGP", "SH", "SHD", "SHN", "SHR", "SHS", "SI", "SIP", "SIS", "SKN", "SMA", "SMAH", "SMC", "SMH", "SMN", "SMR", "SMSH", "SMTS", "SN", "SNA", "SNK", "SP", "SR", "SRH", "SS", "SSA", "SSK", "SSL", "STA", "SZ", "TAA", "TAB", "TAK", "TAR", "TARA", "TAS", "TDR", "TFS", "THT", "TI", "TRA", "UA", "UH", "ZAF", "ZH", "ZMK", "ZNM", "ZS", "ZT", "ZZ"], "total_batches": 10, "total_teachers": 192, "classes": ["59_A", "59_B", "59_C", "59_D", "60_A", "60_B", "60_C", "60_D", "60_E", "60_F", "61_A", "61_B", "61_C", "61_D", "61_E", "61_F", "61_G", "61_H", "61_I", "61_J", "61_K", "61_L", "61_M", "61_N", "61_O", "61_P", "61_Q", "61_R", "61_S", "61_T", "61_U", "61_V", "62_A", "62_B", "62_C", "62_D", "62_E", "62_F", "62_G", "62_H", "63_A", "63_B", "63_C", "63_D", "63_E", "63_F", "63_G", "63_H", "63_I", "63_J", "63_K", "63_L", "63_M", "63_N", "63_O", "64_A", "64_B", "64_C", "64_D", "64_E", "64_F", "64_G", "64_H", "64_I", "64_J", "64_K", "64_L", "64_M", "64_N", "64_O", "65_A", "65_B", "65_C", "65_D", "65_E", "65_F", "65_G", "65_H", "65_I", "65_J", "65_K", "65_L", "65_M", "65_N", "65_O", "65_P", "66_A", "66_B", "66_C", "66_D", "66_E", "66_F", "66_G", "66_H", "66_I", "66_J", "66_K", "66_L", "66_M", "66_N", "66_O", "66_P", "66_Q", "66_R", "67_A", "67_B", "67_C", "67_D", "67_E", "67_F", "67_G", "67_H", "67_I", "67_J", "67_K", "67_L", "67_M", "67_N", "67_O", "67_P", "67_Q", "67_R", "68_A", "68_B", "68_C", "68_D", "68_E", "68_F", "68_G", "68_H", "68_I", "68_J", "68_K", "68_L", "68_M", "68_N", "68_O", "68_P", "68_Q", "68_R", "68_S", "68_T"], "total_classes": 142}