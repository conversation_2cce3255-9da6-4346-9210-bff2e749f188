{"rooms": [{"name": "EMBED LAB-KT-301", "fullName": "EMBED LAB-KT-301", "courses": ["CSE234"]}, {"name": "G1-001", "fullName": "G1-001  (COM LAB)", "courses": ["CSE414", "CSE322", "CSE124", "CSE312", "CSE214"]}, {"name": "G1-002", "fullName": "G1-002  (COM LAB)", "courses": ["TCSE412", "CSE324", "CSE414"]}, {"name": "G1-003", "fullName": "G1-003  (COM LAB)", "courses": ["CSE326", "CSE414", "CSE124", "CSE324"]}, {"name": "G1-004", "fullName": "G1-004  (COM LAB)", "courses": ["TCSE412", "CSE415", "CSE124", "CSE324"]}, {"name": "G1-005", "fullName": "G1-005  (COM LAB)", "courses": ["CSE124", "CSE412", "CSE214"]}, {"name": "G1-006", "fullName": "G1-006  (COM LAB)", "courses": ["CSE124", "CSE312", "CSE332", "CSE214", "TCSE412"]}, {"name": "G1-007", "fullName": "G1-007  (COM LAB)", "courses": ["CSE332", "CSE412", "CSE414"]}, {"name": "G1-008", "fullName": "G1-008  (COM LAB)", "courses": ["CSE214", "CSE114", "CSE322"]}, {"name": "G1-009", "fullName": "G1-009  (COM LAB)", "courses": ["CSE222", "CSE412"]}, {"name": "G1-010", "fullName": "G1-010  (COM LAB)", "courses": ["CSE222", "CSE322", "TCSE412"]}, {"name": "G1-011", "fullName": "G1-011  (COM LAB)", "courses": ["TCSE412", "CSE222", "CSE135"]}, {"name": "G1-012", "fullName": "G1-012  (COM LAB)", "courses": ["CSE322", "CSE222", "CSE312"]}, {"name": "G1-013", "fullName": "G1-013  (COM LAB)", "courses": ["CSE322", "CSE422", "CSE214", "CSE414", "TCSE332"]}, {"name": "G1-014", "fullName": "G1-014  (COM LAB)", "courses": ["CSE322", "CSE415", "CSE332"]}, {"name": "G1-016", "fullName": "G1-016  (COM LAB)", "courses": ["CSE414", "CSE312", "CSE322", "CSE412", "CSE123"]}, {"name": "G1-017", "fullName": "G1-017  (COM LAB)", "courses": ["CSE214", "CSE222", "CSE324", "CSE312"]}, {"name": "G1-018", "fullName": "G1-018  (COM LAB)", "courses": ["CSE332", "CSE124", "CSE312", "CSE414", "CSE314"]}, {"name": "G1-020", "fullName": "G1-020  (COM LAB)", "courses": ["CSE312", "CSE214", "CSE422", "CSE322"]}, {"name": "G1-021", "fullName": "G1-021  (COM LAB)", "courses": ["CSE222", "CSE332", "CSE312", "CSE124"]}, {"name": "G1-022", "fullName": "G1-022  (COM LAB)", "courses": ["CSE412", "CSE214", "CSE124", "CSE414", "CSE415"]}, {"name": "G1-001", "fullName": "G1-001  (COM LAB)", "courses": ["CSE414", "CSE214", "CSE322", "CSE412", "TCSE412", "CSE124", "CSE326", "CSE332", "TCSE332", "CSE324", "CSE312", "CSE415"]}, {"name": "G1-002", "fullName": "G1-002  (COM LAB)", "courses": ["CSE412", "TCSE412", "CSE324", "CSE414", "CSE312", "CSE214", "CSE332"]}, {"name": "G1-003", "fullName": "G1-003  (COM LAB)", "courses": ["CSE326", "CSE312", "TCSE326", "CSE414", "CSE332", "TCSE332", "CSE124", "CSE324"]}, {"name": "G1-004", "fullName": "G1-004  (COM LAB)", "courses": ["CSE412", "CSE124", "CSE214", "CSE415", "CSE326", "TCSE326", "CSE312", "CSE322", "CSE324", "CSE414", "CSE332", "TCSE332"]}, {"name": "G1-005", "fullName": "G1-005  (COM LAB)", "courses": ["CSE124", "CSE322", "CSE214", "CSE422", "CSE412", "CSE326", "TCSE326", "CSE332", "TCSE412"]}, {"name": "G1-006", "fullName": "G1-006  (COM LAB)", "courses": ["CSE124", "CSE324", "CSE312", "CSE422", "TCSE332", "CSE326", "CSE322", "CSE332", "CSE214", "CSE412", "TCSE412"]}, {"name": "G1-007", "fullName": "G1-007  (COM LAB)", "courses": ["TCSE332", "CSE332", "CSE414", "TCSE412", "CSE415", "CSE222", "CSE312", "CSE412"]}, {"name": "G1-008", "fullName": "G1-008  (COM LAB)", "courses": ["CSE214", "CSE332", "CSE114", "CSE222", "TCSE332", "CSE326", "CSE312", "CSE322"]}, {"name": "G1-009", "fullName": "G1-009  (COM LAB)", "courses": ["CSE222", "CSE312", "CSE332", "CSE414", "CSE326", "CSE412", "CSE322", "CSE214", "TCSE412", "CSE124"]}, {"name": "G1-010", "fullName": "G1-010  (COM LAB)", "courses": ["CSE222", "CSE322", "CSE214", "CSE412", "CSE332", "TCSE332", "CSE312"]}, {"name": "G1-011", "fullName": "G1-011  (COM LAB)", "courses": ["CSE412", "CSE312", "CSE322", "TCSE412", "CSE222", "CSE124", "CSE332", "CSE237", "CSE135", "CSE314", "TCSE314"]}, {"name": "G1-012", "fullName": "G1-012  (COM LAB)", "courses": ["CSE322", "CSE222", "CSE124", "CSE412", "TCSE412", "CSE312", "CSE332", "TCSE332", "CSE214", "CSE414"]}, {"name": "G1-013", "fullName": "G1-013  (COM LAB)", "courses": ["CSE322", "CSE422", "CSE414", "CSE214", "CSE332", "TCSE332"]}, {"name": "G1-014", "fullName": "G1-014  (COM LAB)", "courses": ["CSE322", "CSE324", "CSE415", "CSE412", "CSE332", "CSE124", "CSE222", "TCSE332", "CSE312", "CSE214"]}, {"name": "G1-016", "fullName": "G1-016  (COM LAB)", "courses": ["CSE414", "CSE312", "CSE322", "CSE412", "TCSE412", "CSE123", "CSE222"]}, {"name": "G1-017", "fullName": "G1-017  (COM LAB)", "courses": ["CSE214", "CSE124", "CSE222", "CSE412", "CSE324", "CSE322", "CSE312"]}, {"name": "G1-018", "fullName": "G1-018  (COM LAB)", "courses": ["TCSE332", "CSE312", "CSE124", "CSE412", "CSE214", "CSE222", "CSE414", "CSE314"]}, {"name": "G1-020", "fullName": "G1-020  (COM LAB)", "courses": ["CSE312", "CSE214", "CSE322", "CSE412", "TCSE412", "CSE414", "CSE422"]}, {"name": "G1-021", "fullName": "G1-021  (COM LAB)", "courses": ["CSE222", "CSE414", "TCSE332", "CSE124", "CSE214", "CSE312", "CSE412", "CSE324"]}, {"name": "G1-022", "fullName": "G1-022  (COM LAB)", "courses": ["TCSE412", "CSE214", "CSE312", "CSE412", "CSE124", "CSE322", "CSE414", "CSE332", "TCSE332", "CSE415"]}, {"name": "G1-026", "fullName": "G1-026", "courses": ["CSE213", "MAT211", "MAT101", "CSE233", "CSE115", "MAT102", "CSE321", "CSE323", "CSE221", "CSE311", "PHY102", "CSE411", "ENG102", "BNS101", "CSE235", "CSE223", "CSE228", "CSE212", "ACT327", "CSE331"]}, {"name": "G1-027", "fullName": "G1-027", "courses": ["CSE321", "CSE331", "ENG101", "MAT102", "CSE411", "CSE213", "CSE413", "ACT322", "ENG102", "CSE431", "CSE498", "CSE323", "PHY101", "CSE228", "CSE335", "CSE311", "BNS101", "CSE233"]}, {"name": "IOT LAB- KT-502", "fullName": "IOT LAB- KT-502", "courses": ["CSE234"]}, {"name": "IOT LAB- KT-502", "fullName": "IOT LAB- KT-502", "courses": ["CSE234"]}, {"name": "KT-201", "fullName": "KT-201", "courses": ["CSE423", "PHY102", "MAT102", "MAT211", "CSE225", "MAT101", "CSE115", "ACT327", "CSE123", "CSE112", "CSE331", "CSE431", "CSE233", "CSE413", "CSE213", "CSE323", "CSE333", "CSE212", "CSE321"]}, {"name": "KT-208", "fullName": "KT-208", "courses": ["ENG102", "CSE325", "CSE333", "PHY102", "CSE411", "CSE123", "CSE221", "MAT102", "ACT327", "CSE331", "CSE335", "BNS101", "CSE225", "CSE213", "CSE413", "CSE311", "CSE223", "CSE321"]}, {"name": "KT-213", "fullName": "KT-213", "courses": ["CSE212", "CSE333", "CSE411", "CSE321", "CSE123", "CSE335", "MAT101", "PHY102", "ENG101", "CSE325", "CSE414", "CSE228", "CSE423", "CSE213", "CSE136", "CSE236", "CSE323", "CSE225"]}, {"name": "KT-216", "fullName": "KT-216", "courses": ["MAT101", "CSE112", "CSE333", "CSE321", "CSE411", "ENG101", "CSE223", "CSE115", "PHY102", "CSE323", "MAT211", "MAT111", "ACT327", "CSE311", "CSE228", "CSE213", "CSE335", "BNS101", "CSE423", "CSE236"]}, {"name": "KT-217", "fullName": "KT-217", "courses": ["CSE235", "ENG101", "ACT327", "CSE112", "CSE325", "CSE115", "CSE413", "CSE335", "CSE321", "CSE411", "CSE223", "PHY102", "CSE225", "MAT102", "MAT101", "CSE331", "CSE498", "CSE311", "ENG102"]}, {"name": "KT-218", "fullName": "KT-218", "courses": ["ENG102", "CSE112", "CSE321", "MAT101", "ENG101", "CSE228", "MAT211", "CSE225", "PHY102", "CSE115", "BNS101", "CSE431", "CSE325", "CSE233", "CSE423", "CSE445"]}, {"name": "KT-219", "fullName": "KT-219", "courses": ["ENG101", "MAT101", "CSE321", "ENG102", "CSE311", "CSE421", "CSE112", "CSE228", "BNS101", "CSE221", "CSE331", "PHY102", "CSE115", "CSE414", "ACT327", "CSE335", "CSE411"]}, {"name": "KT-220", "fullName": "KT-220", "courses": ["PHY102", "CSE228", "CSE423", "ACT327", "CSE221", "CSE112", "ENG101", "CSE335", "CSE223", "CSE321", "CSE233", "CSE115", "BNS101", "CSE445", "MAT102", "MAT101", "CSE326", "CSE323", "CSE413"]}, {"name": "KT-221", "fullName": "KT-221", "courses": ["ENG101", "CSE112", "CSE221", "PHY102", "MAT211", "CSE212", "CSE214", "CSE331", "MAT101", "MAT102", "CSE115", "BNS101", "CSE321", "CSE233", "ENG102", "CSE431", "CSE323"]}, {"name": "KT-222", "fullName": "KT-222", "courses": ["CSE112", "ENG101", "CSE212", "CSE115", "CSE413", "MAT101", "CSE223", "CSE431", "MAT211", "BNS101", "CSE321", "PHY102", "CSE221", "CSE311", "ACT327", "CSE423", "CSE411", "CSE233"]}, {"name": "KT-223", "fullName": "KT-223", "courses": ["PHY102", "CSE223", "ENG102", "ENG101", "CSE431", "MAT102", "CSE321", "CSE331", "CSE335", "CSE221", "CSE213", "MAT101", "CSE112", "CSE225", "CSE228", "ACT327", "CSE411", "CSE214"]}, {"name": "KT-224", "fullName": "KT-224", "courses": ["CSE311", "CSE427", "CSE113", "MAT211", "CSE321", "CSE225", "CSE335", "CSE212", "ENG102", "CSE223", "BNS101", "CSE498", "CSE112", "ENG101", "CSE411", "CSE431", "CSE413", "CSE123", "CSE221", "ACT327", "CSE315", "CSE331"]}, {"name": "KT-302", "fullName": "KT-302", "courses": ["CSE233", "CSE321", "CSE112", "ENG102", "CSE325", "CSE335", "CSE223", "CSE212", "CSE414", "CSE228", "CSE213", "PHY102", "CSE331", "ACT327", "CSE423", "CSE115", "CSE333", "CSE311", "BNS101", "CSE445"]}, {"name": "KT-303", "fullName": "KT-303", "courses": ["CSE213", "CSE225", "MAT211", "CSE221", "MAT101", "CSE123", "CSE321", "CSE411", "CSE445", "ENG101", "CSE212", "ENG102", "PHY102", "CSE323", "CSE112", "CSE325", "CSE333", "CSE233", "CSE427", "CSE431", "ACT327"]}, {"name": "KT-304", "fullName": "KT-304", "courses": ["CSE311", "CSE213", "CSE225", "CSE335", "CSE228", "CSE325", "CSE411", "CSE221", "CSE212", "CSE223", "CSE421", "BNS101", "CSE112", "CSE123", "CSE321", "PHY102"]}, {"name": "KT-305", "fullName": "KT-305", "courses": ["CSE311", "CSE335", "MAT101", "MAT211", "CSE121", "CSE321", "CSE325", "CSE113", "CSE213", "MAT102", "CSE331", "CSE112", "CSE212", "CSE411", "CSE315", "CSE115", "CSE131", "CSE413", "CSE228", "CSE323", "CSE233"]}, {"name": "KT-306", "fullName": "KT-306", "courses": ["CSE212", "CSE223", "CSE317", "CSE213", "CSE123", "CSE225", "MAT102", "STA227", "CSE333", "CSE321", "CSE335", "BNS101", "CSE131", "MAT211", "CSE 123", "CSE411", "CSE122"]}, {"name": "KT-307", "fullName": "KT-307", "courses": ["CSE221", "CSE225", "CSE212", "CSE123", "MAT211", "CSE411", "CSE215", "CSE213", "CSE311", "CSE427", "CSE228", "CSE115", "ACT327", "CSE445", "CSE223", "MAT102", "CSE112", "CSE431"]}, {"name": "KT-318", "fullName": "KT-318(A)", "courses": ["CSE413", "CSE336", "CSE423", "CSE321", "CSE445", "CSE221", "CSE213", "BNS101", "CSE225", "CSE331", "CSE411", "MAT102", "CSE212", "ENG101", "CSE112", "ACT327", "ENG102", "CSE223", "CSE431", "MAT101", "CSE228", "CSE335", "CSE115", "PHY102", "CSE498", "CSE421", "CSE311"]}, {"name": "KT-320", "fullName": "KT-320", "courses": ["CSE228", "CSE335", "CSE431", "CSE321", "MAT101", "CSE112", "MAT102", "CSE115", "CSE311", "CSE323", "PHY102", "BNS101", "CSE331", "CSE223", "ACT327", "ENG102", "CSE213"]}, {"name": "KT-501", "fullName": "KT-501(A)  (COM LAB)", "courses": ["CSE124", "CSE324", "CSE414", "CSE415", "ACT327", "CSE326", "TCSE326", "CSE422", "CSE222", "CSE331", "CSE323", "BNS101", "CSE332", "CSE221", "MAT211", "CSE322", "CSE413"]}, {"name": "KT-503", "fullName": "KT-503  (COM LAB)", "courses": ["CSE324", "CSE332", "TCSE332", "CSE321", "CSE427", "CSE422", "CSE312", "CSE124", "BNS101", "CSE415", "CSE412", "CSE414"]}, {"name": "KT-504", "fullName": "KT-504  (COM LAB)", "courses": ["CSE332", "CSE431", "CSE324", "CSE422", "MAT102", "CSE326", "CSE225", "TCSE332", "CSE124", "CSE311", "CSE411", "CSE413", "CSE213", "TCSE412", "CSE412", "CSE414"]}, {"name": "KT-510", "fullName": "KT-510  (COM LAB)", "courses": ["CSE414", "CSE222", "CSE332", "CSE423", "CSE415", "CSE326", "CSE312"]}, {"name": "KT-513", "fullName": "KT-513  (COM LAB)", "courses": ["CSE324", "TCSE412", "CSE412", "CSE414", "CSE332", "CSE124", "CSE228", "CSE422", "CSE415"]}, {"name": "KT-514", "fullName": "KT-514", "courses": ["CSE213", "CSE233", "CSE212", "CSE313", "CSE112", "PHY102", "CSE123", "CSE335", "ENG101", "CSE321", "CSE323", "STA227", "MAT102", "CSE423", "ACT327", "CSE431", "ENG102", "PHY101", "CSE221"]}, {"name": "KT-515", "fullName": "KT-515", "courses": ["CSE123", "CSE325", "CSE331", "ENG101", "CSE335", "CSE321", "CSE221", "CSE226", "CSE498", "CSE212", "CSE213", "CSE223", "CSE427", "PHY102", "BNS101", "CSE115"]}, {"name": "KT-516", "fullName": "KT-516", "courses": ["CSE421", "ENG101", "CSE445", "CSE413", "CSE335", "CSE331", "CSE223", "PHY102", "CSE431", "CSE233", "CSE321", "CSE215", "CSE122", "CSE115", "MAT102", "CSE213", "CSE311", "CSE228", "ACT322", "CSE236"]}, {"name": "KT-517", "fullName": "KT-517(A)", "courses": ["CSE223", "CSE413", "CSE233", "CSE213", "CSE411", "CSE212", "MAT211", "CSE228", "CSE115", "CSE321", "CSE112", "ENG102", "CSE225", "MAT102", "MAT101", "ECO237", "CSE335"]}, {"name": "KT-518", "fullName": "KT-518", "courses": ["MAT211", "CSE414", "CSE335", "CSE221", "CSE321", "CSE233", "CSE413", "CSE115", "CSE223", "CSE123", "ENG101", "ENG102", "PHY102", "CSE213", "CSE331", "CSE228", "CSE421", "CSE445"]}, {"name": "KT-801", "fullName": "KT-801(A)", "courses": ["MAT101", "ENG102", "CSE321", "PHY102", "CSE225", "CSE233", "CSE212", "CSE123", "CSE445", "CSE115", "CSE331", "MAT211", "CSE323", "CSE228", "MAT102", "ECO237", "PHY101", "CSE 115", "CSE431", "CSE223", "CSE498", "ENG101", "CSE414", "BNS101"]}, {"name": "KT-802", "fullName": "KT-802", "courses": ["CSE323", "ENG102", "MAT102", "CSE223", "CSE123", "CSE 115", "CSE311", "CSE228", "CSE331", "CSE412", "ACT327", "CSE431", "CSE233", "CSE427", "CSE212", "BNS101", "MAT101", "CSE445"]}, {"name": "KT-803", "fullName": "KT-803", "courses": ["CSE321", "ENG101", "CSE233", "CSE311", "PHY102", "MAT102", "CSE112", "CSE323", "CSE115", "CSE228", "CSE333", "ACT327", "CSE123", "CSE413", "CSE411", "CSE315", "BNS101", "CSE221", "MAT101"]}, {"name": "KT-804", "fullName": "KT-804", "courses": ["CSE233", "MAT211", "CSE331", "CSE431", "CSE223", "CSE423", "CSE123", "PHY102", "MAT111", "ENG101", "CSE321", "CSE112", "CSE323", "MAT101", "MAT102"]}, {"name": "KT-809", "fullName": "KT-809  (Electrical Circuits Lab and Digital Electronics Lab)", "courses": ["CSE133", "CSE224"]}, {"name": "KT-810", "fullName": "KT-810", "courses": ["CSE323", "CSE233", "ACT327"]}, {"name": "KT-813", "fullName": "KT-813(B)", "courses": ["CSE411", "CSE212", "ENG102", "CSE221", "CSE321", "CSE223", "CSE225", "MAT102", "ENG101", "CSE115", "CSE228", "MAT211", "CSE123", "CSE445", "CSE331", "BNS101", "PHY102", "CSE335", "CSE431"]}, {"name": "KT-815", "fullName": "KT-815  (Physics Lab)", "courses": ["PHY103"]}, {"name": "KT-816", "fullName": "KT-816  (Physics Lab)", "courses": ["PHY103"]}, {"name": "KT-916", "fullName": "KT-916", "courses": ["CSE325", "CSE321", "ENG101", "MAT101", "ENG102", "CSE221", "CSE313", "ACT327", "CSE223", "CSE335", "CSE213", "CSE112", "PHY102", "CSE323", "CSE411", "CSE225", "CSE233", "BNS101", "PHY101"]}, {"name": "KT-919", "fullName": "KT-919", "courses": ["CSE335", "MAT102", "CSE228", "CSE331", "CSE413", "CSE311", "CSE411", "CSE423", "PHY102", "CSE221", "CSE225", "ENG101", "CSE123", "CSE323", "CSE223"]}, {"name": "SH-103", "fullName": "SH-103 (Electrical Circuits Lab and Basic Electronics Lab)", "courses": ["CSE224"]}, {"name": "SH-105", "fullName": "SH-105 (Electrical Circuits Lab and Basic Electronics Lab)", "courses": ["CSE224"]}]}