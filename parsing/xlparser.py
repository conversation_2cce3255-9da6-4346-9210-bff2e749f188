import pandas as pd
import json

file_path = "/home/<USER>/Desktop/diu_routine_generator/template/routine_summer_2025_new.xlsx"
excel_data = pd.read_excel(file_path, sheet_name=None, engine="openpyxl")

all_data = {}
for sheet_name, df in excel_data.items():
    df = df.fillna("")
    all_data[sheet_name] = df.to_dict(orient="records")

json_path = "class_routine_summer_2025_new.json"
with open(json_path, "w", encoding="utf-8") as f:
    json.dump(all_data, f, indent=4, ensure_ascii=False)

print(f"Successfully converted to {json_path}")
